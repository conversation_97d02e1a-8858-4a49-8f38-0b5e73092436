[project]
name = "mcp-server-aliyun-observability"
version = "0.2.7"
description = "aliyun observability mcp server"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "mcp>=1.3.0",
    "pydantic>=2.10.0",
    "alibabacloud_arms20190808==8.0.0",
    "alibabacloud_sls20201230==5.7.0",
    "alibabacloud_credentials>=1.0.1",
    "tenacity>=8.0.0",
]

[build-system]
requires = ["hatchling", "wheel", "setuptools"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = ["pyright>=1.1.389"]

[tool.hatch.build.targets.wheel]
packages = ["src/mcp_server_aliyun_observability"]

[tool.hatch.build]
include = [
    "src/**/*.py",
    "README.md",
    "LICENSE",
    "pyproject.toml",
]

exclude = [
    "**/*.pyc",
    "**/__pycache__",
    "**/*.pyo",
    "**/*.pyd",
    "**/*.png",
    ".git",
    ".env",
    ".gitignore",
    "*.so",
    "*.dylib",
    "*.dll",
]

[tool.hatch.metadata]
allow-direct-references = true

[project.optional-dependencies]
dev = ["pytest", "pytest-mock", "pytest-cov"]

[project.urls]


[project.scripts]
mcp-server-aliyun-observability = "mcp_server_aliyun_observability:main" 