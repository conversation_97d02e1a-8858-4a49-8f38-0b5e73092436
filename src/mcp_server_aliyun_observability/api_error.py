TEQ_EXCEPTION_ERROR = [
    {
        "httpStatusCode": 400,
        "errorCode": "RequestTimeExpired",
        "errorMessage": "Request time _requestTime_ has been expired while server time is _server time_.",
        "description": "请求时间和服务端时间差别超过15分钟。",
        "solution": "请您检查请求端时间，稍后重试。",
    },
    {
        "httpStatusCode": 400,
        "errorCode": "ProjectAlreadyExist",
        "errorMessage": "Project _ProjectName_ already exist.",
        "description": "Project名称已存在。Project名称在阿里云地域内全局唯一。",
        "solution": "请您更换Project名称后重试。",
    },
    {
        "httpStatusCode": 401,
        "errorCode": "SignatureNotMatch",
        "errorMessage": "Signature _signature_ not matched.",
        "description": "请求的数字签名不匹配。",
        "solution": "请您重试或更换AccessKey后重试。",
    },
    {
        "httpStatusCode": 401,
        "errorCode": "Unauthorized",
        "errorMessage": "The AccessKeyId is unauthorized.",
        "description": "提供的AccessKey ID值未授权。",
        "solution": "请确认您的AccessKey ID有访问日志服务权限。",
    },
    {
        "httpStatusCode": 401,
        "errorCode": "Unauthorized",
        "errorMessage": "The security token you provided is invalid.",
        "description": "STS Token不合法。",
        "solution": "请检查您的STS接口请求，确认STS Token是合法有效的。",
    },
    {
        "httpStatusCode": 401,
        "errorCode": "Unauthorized",
        "errorMessage": "The security token you provided has expired.",
        "description": "STS Token已经过期。",
        "solution": "请重新申请STS Token后发起请求。",
    },
    {
        "httpStatusCode": 401,
        "errorCode": "Unauthorized",
        "errorMessage": "AccessKeyId not found: _AccessKey ID_",
        "description": "AccessKey ID不存在。",
        "solution": "请检查您的AccessKey ID，重新获取后再发起请求。",
    },
    {
        "httpStatusCode": 401,
        "errorCode": "Unauthorized",
        "errorMessage": "AccessKeyId is disabled: _AccessKey ID_",
        "description": "AccessKey ID是禁用状态。",
        "solution": "请检查您的AccessKey ID，确认为已启用状态后重新发起请求。",
    },
    {
        "httpStatusCode": 401,
        "errorCode": "Unauthorized",
        "errorMessage": "Your SLS service has been forbidden.",
        "description": "日志服务已经被禁用。",
        "solution": "请检查您的日志服务状态，例如是否已欠费。",
    },
    {
        "httpStatusCode": 401,
        "errorCode": "Unauthorized",
        "errorMessage": "The project does not belong to you.",
        "description": "Project不属于当前访问用户。",
        "solution": "请更换Project或者访问用户后重试。",
    },
    {
        "httpStatusCode": 401,
        "errorCode": "InvalidAccessKeyId",
        "errorMessage": "The access key id you provided is invalid: _AccessKey ID_.",
        "description": "AccessKey ID不合法。",
        "solution": "请检查您的AccessKey ID，确认AccessKey ID是合法有效的。",
    },
    {
        "httpStatusCode": 401,
        "errorCode": "InvalidAccessKeyId",
        "errorMessage": "Your SLS service has not opened.",
        "description": "日志服务没有开通。",
        "solution": "请登录日志服务控制台或者通过API开通日志服务后，重新发起请求。",
    },
    {
        "httpStatusCode": 403,
        "errorCode": "WriteQuotaExceed",
        "errorMessage": "Write quota is exceeded.",
        "description": "超过写入日志限额。",
        "solution": "请您优化写入日志请求，减少写入日志数量。",
    },
    {
        "httpStatusCode": 403,
        "errorCode": "ReadQuotaExceed",
        "errorMessage": "Read quota is exceeded.",
        "description": "超过读取日志限额。",
        "solution": "请您优化读取日志请求，减少读取日志数量。",
    },
    {
        "httpStatusCode": 403,
        "errorCode": "MetaOperationQpsLimitExceeded",
        "errorMessage": "Qps limit for the meta operation is exceeded.",
        "description": "超出默认设置的QPS阈值。",
        "solution": "请您优化资源操作请求，减少资源操作次数。建议您延迟几秒后重试。",
    },
    {
        "httpStatusCode": 403,
        "errorCode": "ProjectForbidden",
        "errorMessage": "Project _ProjectName_ has been forbidden.",
        "description": "Project已经被禁用。",
        "solution": "请检查Project状态，您的Project当前可能已经欠费。",
    },
    {
        "httpStatusCode": 404,
        "errorCode": "ProjectNotExist",
        "errorMessage": "The Project does not exist : _name_",
        "description": "日志项目（Project）不存在。",
        "solution": "请您检查Project名称，确认已存在该Project或者地域是否正确。",
    },
    {
        "httpStatusCode": 413,
        "errorCode": "PostBodyTooLarge",
        "errorMessage": "Body size _bodysize_ must little than 10485760.",
        "description": "请求消息体body不能超过10M。",
        "solution": "请您调整请求消息体的大小后重试。",
    },
    {
        "httpStatusCode": 500,
        "errorCode": "InternalServerError",
        "errorMessage": "Internal server error message.",
        "description": "服务器内部错误。",
        "solution": "请您稍后重试。",
    },
    {
        "httpStatusCode": 500,
        "errorCode": "RequestTimeout",
        "errorMessage": "The request is timeout. Please try again later.",
        "description": "请求处理超时。",
        "solution": "请您稍后重试。",
    },
]
